import React, { useState, useEffect } from 'react'
import { Movie, Episode, EpisodeData } from '../types/movie'
import VideoPlayer from './VideoPlayer'
import EpisodeSelector from './EpisodeSelector'

interface MoviePlayerProps {
  movie: Movie & { episodes: Episode[] }
  className?: string
}

const MoviePlayer: React.FC<MoviePlayerProps> = ({ movie, className = '' }) => {
  const [currentEpisode, setCurrentEpisode] = useState<EpisodeData | null>(null)
  const [showEpisodes, setShowEpisodes] = useState(true)

  // Initialize with first episode
  useEffect(() => {
    if (movie.episodes && movie.episodes.length > 0) {
      const firstServer = movie.episodes[0]
      if (firstServer.server_data && firstServer.server_data.length > 0) {
        setCurrentEpisode(firstServer.server_data[0])
      }
    }
  }, [movie.episodes])

  const handleEpisodeSelect = (episode: EpisodeData) => {
    setCurrentEpisode(episode)
  }

  const hasMultipleEpisodes = movie.episodes?.some(server => 
    server.server_data && server.server_data.length > 1
  )

  if (!currentEpisode) {
    return (
      <div className={`bg-gray-800 rounded-lg p-8 text-center ${className}`}>
        <div className="text-white">
          <svg className="w-16 h-16 mx-auto mb-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          <h3 className="text-xl font-semibold mb-2">Không có tập phim khả dụng</h3>
          <p className="text-gray-400">Phim này hiện tại chưa có tập nào để phát.</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Movie Info Header */}
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex flex-col md:flex-row gap-6">
          <img
            src={movie.poster_url}
            alt={movie.name}
            className="w-32 h-48 object-cover rounded-lg mx-auto md:mx-0"
          />
          <div className="flex-1 text-center md:text-left">
            <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">
              {movie.name}
            </h1>
            <h2 className="text-lg text-gray-300 mb-4">
              {movie.origin_name}
            </h2>
            
            <div className="flex flex-wrap gap-4 justify-center md:justify-start text-sm text-gray-400 mb-4">
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
                {movie.year}
              </span>
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
                {movie.time}
              </span>
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clipRule="evenodd" />
                </svg>
                {movie.quality}
              </span>
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
                </svg>
                {movie.lang}
              </span>
            </div>

            <div className="flex flex-wrap gap-2 justify-center md:justify-start mb-4">
              {movie.category?.slice(0, 3).map((cat, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-red-600 text-white text-xs rounded-full"
                >
                  {cat.name}
                </span>
              ))}
            </div>

            <p className="text-gray-300 text-sm leading-relaxed">
              {movie.content?.replace(/<[^>]*>/g, '').substring(0, 200)}
              {movie.content && movie.content.length > 200 && '...'}
            </p>
          </div>
        </div>
      </div>

      {/* Video Player */}
      <div className="bg-gray-900 rounded-lg overflow-hidden">
        <VideoPlayer
          episode={currentEpisode}
          autoPlay={false}
          className="aspect-video w-full"
        />
      </div>

      {/* Episode Selector */}
      {hasMultipleEpisodes && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-white text-xl font-semibold">Tập phim</h3>
            <button
              onClick={() => setShowEpisodes(!showEpisodes)}
              className="text-gray-400 hover:text-white transition-colors md:hidden"
            >
              {showEpisodes ? 'Ẩn' : 'Hiện'} danh sách tập
            </button>
          </div>
          
          <div className={`${showEpisodes ? 'block' : 'hidden md:block'}`}>
            <EpisodeSelector
              episodes={movie.episodes}
              currentEpisode={currentEpisode}
              onEpisodeSelect={handleEpisodeSelect}
            />
          </div>
        </div>
      )}

      {/* Movie Details */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-white text-xl font-semibold mb-4">Thông tin phim</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-gray-300 font-medium mb-2">Mô tả</h4>
            <p className="text-gray-400 text-sm leading-relaxed">
              {movie.content?.replace(/<[^>]*>/g, '') || 'Chưa có mô tả'}
            </p>
          </div>
          
          <div className="space-y-3">
            {movie.director && movie.director.length > 0 && (
              <div>
                <h4 className="text-gray-300 font-medium">Đạo diễn</h4>
                <p className="text-gray-400 text-sm">{movie.director.join(', ')}</p>
              </div>
            )}
            
            {movie.actor && movie.actor.length > 0 && (
              <div>
                <h4 className="text-gray-300 font-medium">Diễn viên</h4>
                <p className="text-gray-400 text-sm">{movie.actor.slice(0, 5).join(', ')}</p>
              </div>
            )}
            
            {movie.country && movie.country.length > 0 && (
              <div>
                <h4 className="text-gray-300 font-medium">Quốc gia</h4>
                <p className="text-gray-400 text-sm">{movie.country.map(c => c.name).join(', ')}</p>
              </div>
            )}
            
            <div>
              <h4 className="text-gray-300 font-medium">Trạng thái</h4>
              <p className="text-gray-400 text-sm">{movie.status}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MoviePlayer
