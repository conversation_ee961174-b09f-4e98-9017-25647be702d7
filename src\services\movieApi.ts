import { API_ENDPOINTS } from '../types/movie'
import type { ApiResponse, MovieDetail, MovieType, SortField, SortType } from '../types/movie'

class MovieApiService {
  private baseUrl = API_ENDPOINTS.BASE_URL

  // Helper method to build query string
  private buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams()

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString())
      }
    })

    return searchParams.toString()
  }

  // Get new movies
  async getNewMovies(page: number = 1): Promise<ApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.NEW_MOVIES}?page=${page}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      })
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()
      console.log('Raw API Response:', data) // Debug log
      return data
    } catch (error) {
      console.error('Error fetching new movies:', error)
      // Return mock data as fallback
      return this.getMockMoviesData()
    }
  }

  // Mock data fallback
  private getMockMoviesData(): ApiResponse {
    return {
      status: true,
      msg: "Mock data - API không khả dụng",
      data: {
        seoOnPage: {
          og_type: "website",
          titleHead: "Phim mới cập nhật",
          descriptionHead: "Danh sách phim mới cập nhật",
          og_image: [],
          og_url: ""
        },
        breadCrumb: [
          { name: "Trang chủ", slug: "", isCurrent: false },
          { name: "Phim mới", isCurrent: true }
        ],
        titlePage: "Phim mới cập nhật",
        items: [
          {
            _id: "mock-1",
            name: "Spider-Man: No Way Home",
            slug: "spider-man-no-way-home",
            origin_name: "Spider-Man: No Way Home",
            poster_url: "https://images.unsplash.com/photo-1635805737707-575885ab0820?w=300&h=450&fit=crop",
            thumb_url: "https://images.unsplash.com/photo-1635805737707-575885ab0820?w=300&h=450&fit=crop",
            year: 2021,
            content: "Peter Parker's secret identity is revealed to the entire world. Desperate for help, Peter turns to Doctor Strange to make the world forget that he is Spider-Man.",
            type: "single",
            status: "completed",
            time: "148 phút",
            episode_current: "Full",
            episode_total: "1",
            quality: "HD",
            lang: "Vietsub",
            notify: "",
            showtimes: "",
            trailer_url: "",
            category: [
              { id: "1", name: "Hành Động", slug: "hanh-dong" },
              { id: "2", name: "Phiêu Lưu", slug: "phieu-luu" },
              { id: "3", name: "Khoa Học Viễn Tưởng", slug: "khoa-hoc-vien-tuong" }
            ],
            country: [{ id: "1", name: "Mỹ", slug: "my" }],
            actor: ["Tom Holland", "Zendaya", "Benedict Cumberbatch"],
            director: ["Jon Watts"],
            chieurap: false,
            tmdb: {
              type: "movie",
              id: "634649",
              season: null,
              vote_average: 8.4,
              vote_count: 15000
            },
            imdb: { id: "tt10872600" },
            created: { time: "2024-01-01T00:00:00.000Z" },
            modified: { time: "2024-01-01T00:00:00.000Z" }
          },
          {
            _id: "mock-2",
            name: "Avengers: Endgame",
            slug: "avengers-endgame",
            origin_name: "Avengers: Endgame",
            poster_url: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=450&fit=crop",
            thumb_url: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=450&fit=crop",
            year: 2019,
            content: "After the devastating events of Avengers: Infinity War, the universe is in ruins. With the help of remaining allies, the Avengers assemble once more.",
            type: "single",
            status: "completed",
            time: "181 phút",
            episode_current: "Full",
            episode_total: "1",
            quality: "HD",
            lang: "Vietsub",
            notify: "",
            showtimes: "",
            trailer_url: "",
            category: [
              { id: "1", name: "Hành Động", slug: "hanh-dong" },
              { id: "2", name: "Phiêu Lưu", slug: "phieu-luu" },
              { id: "3", name: "Khoa Học Viễn Tưởng", slug: "khoa-hoc-vien-tuong" }
            ],
            country: [{ id: "1", name: "Mỹ", slug: "my" }],
            actor: ["Robert Downey Jr.", "Chris Evans", "Mark Ruffalo"],
            director: ["Anthony Russo", "Joe Russo"],
            chieurap: false,
            tmdb: {
              type: "movie",
              id: "299534",
              season: null,
              vote_average: 8.4,
              vote_count: 20000
            },
            imdb: { id: "tt4154796" },
            created: { time: "2024-01-01T00:00:00.000Z" },
            modified: { time: "2024-01-01T00:00:00.000Z" }
          }
        ],
        params: {
          type_slug: "phim-moi-cap-nhat",
          filterCategory: [],
          filterCountry: [],
          filterYear: "",
          filterType: "",
          sortField: "modified.time",
          sortType: "desc",
          pagination: {
            totalItems: 2,
            totalItemsPerPage: 20,
            currentPage: 1,
            totalPages: 1
          }
        },
        type_list: "phim-moi-cap-nhat",
        APP_DOMAIN_FRONTEND: "http://localhost:5173",
        APP_DOMAIN_CDN_IMAGE: "https://images.unsplash.com"
      }
    }
  }

  // Get movies by type (phim-bo, phim-le, tv-shows, hoat-hinh)
  async getMoviesByType(
    type: MovieType,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      category?: string
      country?: string
      year?: number
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        category: options.category,
        country: options.country,
        year: options.year,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.LIST}/${type}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching ${type} movies:`, error)
      throw error
    }
  }

  // Get movies by category
  async getMoviesByCategory(
    category: string,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      country?: string
      year?: number
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        country: options.country,
        year: options.year,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.CATEGORY}/${category}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching movies by category ${category}:`, error)
      throw error
    }
  }

  // Get movies by country
  async getMoviesByCountry(
    country: string,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      category?: string
      year?: number
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        category: options.category,
        year: options.year,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.COUNTRY}/${country}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching movies by country ${country}:`, error)
      throw error
    }
  }

  // Get movies by year
  async getMoviesByYear(
    year: number,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      category?: string
      country?: string
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        category: options.category,
        country: options.country,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.YEAR}/${year}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching movies by year ${year}:`, error)
      throw error
    }
  }

  // Search movies
  async searchMovies(
    keyword: string,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      category?: string
      country?: string
      year?: number
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        keyword,
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        category: options.category,
        country: options.country,
        year: options.year,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.SEARCH}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error searching movies with keyword "${keyword}":`, error)
      throw error
    }
  }

  // Get movie detail
  async getMovieDetail(slug: string): Promise<MovieDetail> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.MOVIE_DETAIL}/${slug}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching movie detail for slug "${slug}":`, error)
      // Return mock movie detail as fallback
      return this.getMockMovieDetail(slug)
    }
  }

  // Mock movie detail fallback
  private getMockMovieDetail(slug: string): MovieDetail {
    return {
      status: true,
      msg: "Mock data - API không khả dụng",
      movie: {
        _id: "mock-detail-1",
        name: "Spider-Man: No Way Home",
        slug: slug,
        origin_name: "Spider-Man: No Way Home",
        poster_url: "https://images.unsplash.com/photo-1635805737707-575885ab0820?w=300&h=450&fit=crop",
        thumb_url: "https://images.unsplash.com/photo-1635805737707-575885ab0820?w=300&h=450&fit=crop",
        year: 2021,
        content: "Peter Parker's secret identity is revealed to the entire world. Desperate for help, Peter turns to Doctor Strange to make the world forget that he is Spider-Man. The spell goes wrong and dangerous foes from other worlds start to appear, forcing Peter to discover what it truly means to be Spider-Man.",
        type: "single",
        status: "completed",
        time: "148 phút",
        episode_current: "Full",
        episode_total: "1",
        quality: "HD",
        lang: "Vietsub",
        notify: "",
        showtimes: "",
        trailer_url: "",
        category: [
          { id: "1", name: "Hành Động", slug: "hanh-dong" },
          { id: "2", name: "Phiêu Lưu", slug: "phieu-luu" },
          { id: "3", name: "Khoa Học Viễn Tưởng", slug: "khoa-hoc-vien-tuong" }
        ],
        country: [{ id: "1", name: "Mỹ", slug: "my" }],
        actor: ["Tom Holland", "Zendaya", "Benedict Cumberbatch", "Jacob Batalon", "Jon Favreau"],
        director: ["Jon Watts"],
        chieurap: false,
        tmdb: {
          type: "movie",
          id: "634649",
          season: null,
          vote_average: 8.4,
          vote_count: 15000
        },
        imdb: { id: "tt10872600" },
        created: { time: "2024-01-01T00:00:00.000Z" },
        modified: { time: "2024-01-01T00:00:00.000Z" },
        episodes: [
          {
            server_name: "VIP Server",
            server_data: [
              {
                name: "Full",
                slug: "full",
                filename: "spider-man-no-way-home-full.mp4",
                link_embed: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                link_m3u8: ""
              }
            ]
          },
          {
            server_name: "Server 2",
            server_data: [
              {
                name: "Full HD",
                slug: "full-hd",
                filename: "spider-man-no-way-home-hd.mp4",
                link_embed: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
                link_m3u8: ""
              }
            ]
          }
        ]
      }
    }
  }

  // Get trending movies (new movies with high rating)
  async getTrendingMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getNewMovies(1)
  }

  // Get popular movies (sorted by vote_average)
  async getPopularMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByType('phim-le', {
      sortField: 'modified.time',
      sortType: 'desc',
      limit
    })
  }

  // Get action movies
  async getActionMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByCategory('hanh-dong', { limit })
  }

  // Get comedy movies
  async getComedyMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByCategory('hai-huoc', { limit })
  }

  // Get TV shows
  async getTVShows(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByType('tv-shows', { limit })
  }
}

// Export singleton instance
export const movieApi = new MovieApiService()
export default movieApi
