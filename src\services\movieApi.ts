import { API_ENDPOINTS } from '../types/movie'
import type { ApiResponse, MovieDetail, MovieType, SortField, SortType } from '../types/movie'

class MovieApiService {
  private baseUrl = API_ENDPOINTS.BASE_URL

  // Helper method to build query string
  private buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams()

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString())
      }
    })

    return searchParams.toString()
  }

  // Get new movies
  async getNewMovies(page: number = 1): Promise<ApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.NEW_MOVIES}?page=${page}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      })
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()
      console.log('Raw API Response:', data) // Debug log
      return data
    } catch (error) {
      console.error('Error fetching new movies:', error)
      // Return mock data as fallback
      return this.getMockMoviesData()
    }
  }

  // Mock data fallback
  private getMockMoviesData(): ApiResponse {
    return {
      status: true,
      msg: "Mock data - API không khả dụng",
      data: {
        seoOnPage: {
          og_type: "website",
          titleHead: "Phim mới cập nhật",
          descriptionHead: "Danh sách phim mới cập nhật",
          og_image: [],
          og_url: ""
        },
        breadCrumb: [
          { name: "Trang chủ", slug: "", isCurrent: false },
          { name: "Phim mới", isCurrent: true }
        ],
        titlePage: "Phim mới cập nhật",
        items: [
          {
            _id: "15341840eedadf2f53ad8571ac6078a2",
            name: "Ngôi Trường Xác Sống",
            slug: "ngoi-truong-xac-song",
            origin_name: "All of Us Are Dead",
            poster_url: "https://phimimg.com/upload/vod/20250325-1/6db202d6161c123d96b0180c2da9b1e5.jpg",
            thumb_url: "https://phimimg.com/upload/vod/20250325-1/6985255433cba78af7f28fe63c5126c9.jpg",
            year: 2022,
            content: "Một trường cấp ba trở thành điểm bùng phát virus thây ma. Các học sinh mắc kẹt phải nỗ lực thoát ra – hoặc biến thành một trong những người nhiễm bệnh hung tợn.",
            type: "series",
            status: "completed",
            time: "65 phút/tập",
            episode_current: "Hoàn Tất (12/12)",
            episode_total: "12",
            quality: "FHD",
            lang: "Vietsub + Lồng Tiếng",
            notify: "",
            showtimes: "",
            trailer_url: "https://www.youtube.com/watch?v=IN5TD4VRcSM",
            is_copyright: false,
            sub_docquyen: false,
            chieurap: false,
            view: 1,
            category: [
              { id: "9822be111d2ccc29c7172c78b8af8ff5", name: "Hành Động", slug: "hanh-dong" },
              { id: "66c78b23908113d478d8d85390a244b4", name: "Phiêu Lưu", slug: "phieu-luu" },
              { id: "37a7b38b6184a5ebd3c43015aa20709d", name: "Chính Kịch", slug: "chinh-kich" },
              { id: "0bcf4077916678de9b48c89221fcf8ae", name: "Khoa Học", slug: "khoa-hoc" },
              { id: "68564911f00849030f9c9c144ea1b931", name: "Viễn Tưởng", slug: "vien-tuong" }
            ],
            country: [{ id: "05de95be5fc404da9680bbb3dd8262e6", name: "Hàn Quốc", slug: "han-quoc" }],
            actor: ["Park Ji-hu", "Yoon Chan-young", "Cho Yi-hyun", "Lomon", "Yoo In-soo", "Lee You-mi", "Kim Byung-chul", "Lee Kyoo-hyung", "Jeon Bae-soo"],
            director: ["Đang cập nhật"],
            tmdb: {
              type: "tv",
              id: "99966",
              season: 2,
              vote_average: 8.287,
              vote_count: 4010
            },
            imdb: { id: null },
            created: { time: "2024-05-24T02:45:20.000Z" },
            modified: { time: "2025-03-25T17:16:13.000Z" }
          },
          {
            _id: "mock-squid-game",
            name: "Squid Game",
            slug: "squid-game",
            origin_name: "오징어 게임",
            poster_url: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=450&fit=crop",
            thumb_url: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=450&fit=crop",
            year: 2021,
            content: "Hàng trăm người chơi đang gặp khó khăn về tài chính chấp nhận lời mời tham gia các trò chơi trẻ em để giành giải thưởng tiền mặt khổng lồ. Nhưng cái giá phải trả lại rất đắt.",
            type: "series",
            status: "completed",
            time: "60 phút/tập",
            episode_current: "Hoàn Tất (9/9)",
            episode_total: "9",
            quality: "FHD",
            lang: "Vietsub + Lồng Tiếng",
            notify: "",
            showtimes: "",
            trailer_url: "https://www.youtube.com/watch?v=oqxAJKy0ii4",
            is_copyright: false,
            sub_docquyen: false,
            chieurap: false,
            view: 1,
            category: [
              { id: "9822be111d2ccc29c7172c78b8af8ff5", name: "Hành Động", slug: "hanh-dong" },
              { id: "37a7b38b6184a5ebd3c43015aa20709d", name: "Chính Kịch", slug: "chinh-kich" },
              { id: "thriller", name: "Kinh Dị", slug: "kinh-di" }
            ],
            country: [{ id: "05de95be5fc404da9680bbb3dd8262e6", name: "Hàn Quốc", slug: "han-quoc" }],
            actor: ["Lee Jung-jae", "Park Hae-soo", "Wi Ha-jun", "Jung Ho-yeon", "O Yeong-su"],
            director: ["Hwang Dong-hyuk"],
            tmdb: {
              type: "tv",
              id: "93405",
              season: 1,
              vote_average: 7.8,
              vote_count: 8500
            },
            imdb: { id: "tt10919420" },
            created: { time: "2024-01-01T00:00:00.000Z" },
            modified: { time: "2024-01-01T00:00:00.000Z" }
          }
        ],
        params: {
          type_slug: "phim-moi-cap-nhat",
          filterCategory: [],
          filterCountry: [],
          filterYear: "",
          filterType: "",
          sortField: "modified.time",
          sortType: "desc",
          pagination: {
            totalItems: 2,
            totalItemsPerPage: 20,
            currentPage: 1,
            totalPages: 1
          }
        },
        type_list: "phim-moi-cap-nhat",
        APP_DOMAIN_FRONTEND: "http://localhost:5173",
        APP_DOMAIN_CDN_IMAGE: "https://images.unsplash.com"
      }
    }
  }

  // Get movies by type (phim-bo, phim-le, tv-shows, hoat-hinh)
  async getMoviesByType(
    type: MovieType,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      category?: string
      country?: string
      year?: number
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        category: options.category,
        country: options.country,
        year: options.year,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.LIST}/${type}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching ${type} movies:`, error)
      throw error
    }
  }

  // Get movies by category
  async getMoviesByCategory(
    category: string,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      country?: string
      year?: number
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        country: options.country,
        year: options.year,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.CATEGORY}/${category}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching movies by category ${category}:`, error)
      throw error
    }
  }

  // Get movies by country
  async getMoviesByCountry(
    country: string,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      category?: string
      year?: number
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        category: options.category,
        year: options.year,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.COUNTRY}/${country}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching movies by country ${country}:`, error)
      throw error
    }
  }

  // Get movies by year
  async getMoviesByYear(
    year: number,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      category?: string
      country?: string
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        category: options.category,
        country: options.country,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.YEAR}/${year}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching movies by year ${year}:`, error)
      throw error
    }
  }

  // Search movies
  async searchMovies(
    keyword: string,
    options: {
      page?: number
      sortField?: SortField
      sortType?: SortType
      category?: string
      country?: string
      year?: number
      limit?: number
    } = {}
  ): Promise<ApiResponse> {
    try {
      const queryString = this.buildQueryString({
        keyword,
        page: options.page || 1,
        sort_field: options.sortField || 'modified.time',
        sort_type: options.sortType || 'desc',
        category: options.category,
        country: options.country,
        year: options.year,
        limit: options.limit || 20
      })

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.SEARCH}?${queryString}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error searching movies with keyword "${keyword}":`, error)
      throw error
    }
  }

  // Get movie detail
  async getMovieDetail(slug: string): Promise<MovieDetail> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.MOVIE_DETAIL}/${slug}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error(`Error fetching movie detail for slug "${slug}":`, error)
      // Return mock movie detail as fallback
      return this.getMockMovieDetail(slug)
    }
  }

  // Mock movie detail fallback
  private getMockMovieDetail(slug: string): MovieDetail {
    return {
      status: true,
      msg: "Mock data - API không khả dụng",
      movie: {
        _id: "15341840eedadf2f53ad8571ac6078a2",
        name: "Ngôi Trường Xác Sống",
        slug: slug,
        origin_name: "All of Us Are Dead",
        poster_url: "https://phimimg.com/upload/vod/20250325-1/6db202d6161c123d96b0180c2da9b1e5.jpg",
        thumb_url: "https://phimimg.com/upload/vod/20250325-1/6985255433cba78af7f28fe63c5126c9.jpg",
        year: 2022,
        content: "Một trường cấp ba trở thành điểm bùng phát virus thây ma. Các học sinh mắc kẹt phải nỗ lực thoát ra – hoặc biến thành một trong những người nhiễm bệnh hung tợn.",
        type: "series",
        status: "completed",
        time: "65 phút/tập",
        episode_current: "Hoàn Tất (12/12)",
        episode_total: "12",
        quality: "FHD",
        lang: "Vietsub + Lồng Tiếng",
        notify: "",
        showtimes: "",
        trailer_url: "https://www.youtube.com/watch?v=IN5TD4VRcSM",
        is_copyright: false,
        sub_docquyen: false,
        chieurap: false,
        view: 1,
        category: [
          { id: "9822be111d2ccc29c7172c78b8af8ff5", name: "Hành Động", slug: "hanh-dong" },
          { id: "66c78b23908113d478d8d85390a244b4", name: "Phiêu Lưu", slug: "phieu-luu" },
          { id: "37a7b38b6184a5ebd3c43015aa20709d", name: "Chính Kịch", slug: "chinh-kich" },
          { id: "0bcf4077916678de9b48c89221fcf8ae", name: "Khoa Học", slug: "khoa-hoc" },
          { id: "68564911f00849030f9c9c144ea1b931", name: "Viễn Tưởng", slug: "vien-tuong" }
        ],
        country: [{ id: "05de95be5fc404da9680bbb3dd8262e6", name: "Hàn Quốc", slug: "han-quoc" }],
        actor: ["Park Ji-hu", "Yoon Chan-young", "Cho Yi-hyun", "Lomon", "Yoo In-soo", "Lee You-mi", "Kim Byung-chul", "Lee Kyoo-hyung", "Jeon Bae-soo"],
        director: ["Đang cập nhật"],
        tmdb: {
          type: "tv",
          id: "99966",
          season: 2,
          vote_average: 8.287,
          vote_count: 4010
        },
        imdb: { id: null },
        created: { time: "2024-05-24T02:45:20.000Z" },
        modified: { time: "2025-03-25T17:16:13.000Z" },
        episodes: [
          {
            server_name: "#Hà Nội (Vietsub)",
            server_data: [
              {
                name: "Tập 01",
                slug: "tap-01",
                filename: "Ngôi Trường Xác Sống - All of Us Are Dead - 2022 - 1080p - Vietsub - Tập 01",
                link_embed: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                link_m3u8: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
              },
              {
                name: "Tập 02",
                slug: "tap-02",
                filename: "Ngôi Trường Xác Sống - All of Us Are Dead - 2022 - 1080p - Vietsub - Tập 02",
                link_embed: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
                link_m3u8: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4"
              },
              {
                name: "Tập 03",
                slug: "tap-03",
                filename: "Ngôi Trường Xác Sống - All of Us Are Dead - 2022 - 1080p - Vietsub - Tập 03",
                link_embed: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4",
                link_m3u8: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4"
              }
            ]
          },
          {
            server_name: "#Hà Nội (Lồng Tiếng)",
            server_data: [
              {
                name: "Tập 01",
                slug: "tap-01",
                filename: "Ngôi Trường Xác Sống - All of Us Are Dead - 2022 - 1080p - Lồng Tiếng - Tập 01",
                link_embed: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4",
                link_m3u8: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4"
              },
              {
                name: "Tập 02",
                slug: "tap-02",
                filename: "Ngôi Trường Xác Sống - All of Us Are Dead - 2022 - 1080p - Lồng Tiếng - Tập 02",
                link_embed: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4",
                link_m3u8: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4"
              },
              {
                name: "Tập 03",
                slug: "tap-03",
                filename: "Ngôi Trường Xác Sống - All of Us Are Dead - 2022 - 1080p - Lồng Tiếng - Tập 03",
                link_embed: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4",
                link_m3u8: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4"
              }
            ]
          }
        ]
      }
    }
  }

  // Get trending movies (new movies with high rating)
  async getTrendingMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getNewMovies(1)
  }

  // Get popular movies (sorted by vote_average)
  async getPopularMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByType('phim-le', {
      sortField: 'modified.time',
      sortType: 'desc',
      limit
    })
  }

  // Get action movies
  async getActionMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByCategory('hanh-dong', { limit })
  }

  // Get comedy movies
  async getComedyMovies(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByCategory('hai-huoc', { limit })
  }

  // Get TV shows
  async getTVShows(limit: number = 20): Promise<ApiResponse> {
    return this.getMoviesByType('tv-shows', { limit })
  }
}

// Export singleton instance
export const movieApi = new MovieApiService()
export default movieApi
